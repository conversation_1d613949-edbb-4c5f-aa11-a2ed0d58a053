// Sistema de Proteção Anti-Inspeção Simplificado
export const initProtection = () => {
  try {
    // Aguardar DOM estar pronto
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initSecurityProtections);
    } else {
      initSecurityProtections();
    }
  } catch (error) {
    console.warn('Proteção não pôde ser inicializada:', error);
  }
};

function initSecurityProtections() {
  try {
    // Desabilitar F12 e atalhos de desenvolvedor
    document.addEventListener('keydown', (e) => {
      // F12
      if (e.keyCode === 123) {
        e.preventDefault();
        return false;
      }
      // Ctrl+Shift+I (DevTools)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
        e.preventDefault();
        return false;
      }
      // Ctrl+Shift+C (Inspect Element)
      if (e.ctrl<PERSON>ey && e.shiftKey && e.keyCode === 67) {
        e.preventDefault();
        return false;
      }
      // Ctrl+Shift+J (Console)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
        e.preventDefault();
        return false;
      }
      // Ctrl+U (View Source)
      if (e.ctrlKey && e.keyCode === 85) {
        e.preventDefault();
        return false;
      }
      // Ctrl+S (Save)
      if (e.ctrlKey && e.keyCode === 83) {
        e.preventDefault();
        return false;
      }
      // Print Screen
      if (e.keyCode === 44) {
        e.preventDefault();
        return false;
      }
    }, true);

    // Desabilitar menu de contexto
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      return false;
    }, true);

    // Desabilitar seleção de texto
    document.addEventListener('selectstart', (e) => {
      e.preventDefault();
      return false;
    }, true);

    // Adicionar CSS de proteção
    addProtectionCSS();

    // Limpar console periodicamente
    setInterval(() => {
      try {
        console.clear();
      } catch (e) {}
    }, 2000);

  } catch (error) {
    console.warn('Erro ao inicializar proteções:', error);
  }
}

function addProtectionCSS() {
  try {
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
        -webkit-touch-callout: none !important;
      }
      input, textarea, [contenteditable="true"] {
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
      }
      ::selection {
        background: transparent !important;
      }
      ::-moz-selection {
        background: transparent !important;
      }
    `;
    document.head.appendChild(style);
  } catch (error) {
    console.warn('Erro ao adicionar CSS de proteção:', error);
  }
}
