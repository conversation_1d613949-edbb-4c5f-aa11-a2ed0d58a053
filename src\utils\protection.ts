// Proteção básica do sistema
export function initProtection() {
  // Proteção básica - pode ser expandida depois
  console.log('🔒 Sistema de proteção inicializado');
  
  // Desabilitar F12 (temporariamente desabilitado para desenvolvimento)
  // document.addEventListener('keydown', function(e) {
  //   if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
  //     e.preventDefault();
  //     return false;
  //   }
  // });
  
  // Desabilitar clique direito (temporariamente desabilitado)
  // document.addEventListener('contextmenu', function(e) {
  //   e.preventDefault();
  //   return false;
  // });
}
